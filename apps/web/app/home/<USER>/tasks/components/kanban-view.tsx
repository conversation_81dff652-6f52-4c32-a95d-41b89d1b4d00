"use client"

import { useCallback, useMemo, useState } from "react"
import { CompanyContent } from "~/types/company-content"
import { statuses } from "../data/data"
import { KanbanColumn, KanbanTask } from "../../../../components/kanban/lib/types"
import { CustomKanbanWrapper } from "./custom-kanban-wrapper"
import { Circle, Clock, Check } from "lucide-react"
import { useZero } from "~/hooks/use-zero"
import { useQuery as useZeroQuery } from "@rocicorp/zero/react"
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace"
interface KanbanViewProps {
  tasks: CompanyContent[]
}

export function KanbanView({ tasks }: KanbanViewProps) {
  
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [ company_task_statuses ]= useZeroQuery(
    zero.query.company_task_statuses
    .where("company_id", "=", workspace.account.id)
    .orderBy('status_order', 'asc')
    ,
    {
      ttl: '1d'
    }
  );

  // Create columns based on statuses (either from company-specific or fallback to default)
  const columns = useMemo(() => {
    // If we have company statuses, use those
    if (company_task_statuses && company_task_statuses.length > 0) {
      return company_task_statuses.map((status, index) => {
        return {
          id: status.name,
          name: status.display_name,
          nextColumnId: index < company_task_statuses.length - 1 ? company_task_statuses?.[index + 1]?.name : null,
          color: status.color,
          icon: status.icon,
        } as KanbanColumn
      })
    }

    // Otherwise use default statuses
    return statuses.map((status, index) => {
      return {
        id: status.value,
        name: status.label,
        nextColumnId: index < statuses.length - 1 ? statuses?.[index + 1]?.value : null,
        icon: status.icon.name,
      } as KanbanColumn
    })
  }, [company_task_statuses])

  // Organize tasks by column/status
  // Get the icon component based on icon name
  const getIconComponent = useCallback((iconName: string) => {
    switch (iconName) {
      case 'Circle': return Circle;
      case 'Clock': return Clock;
      case 'Check': return Check;
      default: return Circle;
    }
  }, []);

  const columnsWithTasks = useMemo(() => {
    return columns.map((column) => {
      const columnTasks = tasks
        .filter((task) => {
          // Handle both lowercase and normal case status values 
          return (task.status?.toLowerCase() === column?.id?.toLowerCase()) || 
                 (!task.status && column.id === 'draft');
        })
        .map((task) => {
          // Get matching status for this task
          const taskStatus = company_task_statuses?.find(s => s.name === (task.status || 'draft')) || 
                            { color: '#999', icon: 'Circle' };
          
          // Get the icon component
          const IconComponent = getIconComponent(taskStatus.icon || 'Circle');
          
          return {
            id: task.id,
            name: task.task_title || task.content_type,
            columnId: task.status || 'draft',
            position: task.id, // Using id as position for simplicity
            content: (
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-muted-foreground">ID: {task.id.substring(0, 8)}...</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center" style={{ color: taskStatus.color || '#999' }}>
                    <IconComponent className="mr-1 h-3 w-3" />
                    <span className="text-xs">{column.name}</span>
                  </div>
                  <span className="text-xs bg-primary/10 rounded-sm px-1 py-0.5">{task.content_type}</span>
                  {task.channel && (
                    <span className="text-xs bg-primary/10 rounded-sm px-1 py-0.5">{task.channel}</span>
                  )}
                </div>
              </div>
            ),
          };
        }) as KanbanTask[]

      return {
        ...column,
        tasks: columnTasks,
      }
    })
  }, [columns, tasks, company_task_statuses, getIconComponent])

  const handleTaskClick = useCallback((task: KanbanTask) => {
    // In a real implementation, you might want to open a detailed view of the task
    console.log("Task clicked:", task)
  }, [])

  const handleTaskMove = useCallback(
    async (taskId: string, columnId: string | null | undefined, newIndex: number) => {
      // Update local state
      console.log("Task moved:", taskId, columnId, newIndex)
      zero.mutate.company_content.update({
        id: taskId,
        values: {
          status: columnId,
          kanban_order: newIndex
        }
      })
    },
    []
  )

  return (
    <div className="rounded-md border border-border bg-background p-4 h-[75vh]">
      <CustomKanbanWrapper
        columns={columnsWithTasks}
        onTaskClick={handleTaskClick}
        onTaskMoveRequested={handleTaskMove}
      />
    </div>
  )
}