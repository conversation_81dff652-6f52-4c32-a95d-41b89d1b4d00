"use client"

import { useState, useEffect, use<PERSON>em<PERSON> } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@kit/ui/dialog"
import { Button } from "@kit/ui/button"
import { Input } from "@kit/ui/input"
import { Textarea } from "@kit/ui/textarea"
import { Label } from "@kit/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select"
import { CompanyContent } from "~/types/company-content"
import { getCompanyTaskStatuses, TaskStatus } from "~/services/task-status"
import { useQuery } from "@tanstack/react-query"
import { Archive, Maximize2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace"
import { useZero } from "~/hooks/use-zero"
import { useQuery as useZeroQuery } from '@rocicorp/zero/react'

interface TaskEditDialogProps {
  task: CompanyContent
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface TaskCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  campaignId?: string | null
}

export function TaskEditDialog({ task, open, onOpenChange }: TaskEditDialogProps) {
  const workspace = useTeamAccountWorkspace();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const zero = useZero();
  const [formData, setFormData] = useState({
    task_title: task.task_title,
    task_description: task.task_description || "",
    status: task.status || "draft",
    assigned_to: task.assigned_to || "",
    scheduled_publishing_time: task.scheduled_publishing_time ? 
      new Date(task.scheduled_publishing_time).toISOString().slice(0, 16) : "",
  })

  // Get all memberships for this company
  const [memberships] = useZeroQuery(
    zero.query.accounts_memberships.where('account_id', '=', workspace.account.id),
    {
      ttl: '1d'
    }
  );

  // Get all accounts (users) from memberships
  const [accounts] = useZeroQuery(zero.query.accounts, {
    ttl: '1d'
  });

  // Get company members with their account details
  const companyMembers = useMemo(() => {
    if (!memberships || !accounts) return [];
    
    return memberships.map(membership => {
      const account = accounts.find(acc => acc.id === membership.user_id);
      return {
        id: membership.user_id,
        name: account?.name || 'Unknown User',
        email: account?.email || '',
        role: membership.account_role
      };
    }).filter(member => member.name !== 'Unknown User');
  }, [memberships, accounts]);

  // Add this useEffect to update form data when task changes
  useEffect(() => {
    setFormData({
      task_title: task.task_title,
      task_description: task.task_description || "",
      status: task.status || "draft",
      assigned_to: task.assigned_to || "",
      scheduled_publishing_time: task.scheduled_publishing_time ? 
        new Date(task.scheduled_publishing_time).toISOString().slice(0, 16) : "",
    });
  }, [task]);

  // Fetch company task statuses
  const { data: taskStatuses } = useQuery({
    queryKey: ['taskStatuses', task.company_id],
    queryFn: () => getCompanyTaskStatuses(task.company_id || ''),
    enabled: !!task.company_id,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    try {
      // Prepare the update values
      const updateValues: any = {
        task_title: formData.task_title,
        task_description: formData.task_description,
        status: formData.status,
        updated_at: new Date().toISOString()
      };

      // Handle assigned_to properly - explicitly set null for unassignment
      if (formData.assigned_to === "" || formData.assigned_to === "unassigned") {
        updateValues.assigned_to = null;
      } else if (formData.assigned_to) {
        updateValues.assigned_to = formData.assigned_to;
      }

      (zero.mutate.company_content as any).update({
        id: task.id,
        values: updateValues
      })
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to update task:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenInStudio = () => {
    
    router.push(`/home/<USER>/studio/${task.id}`)
  }

  const archiveTask = (task: CompanyContent) => {
    (zero.mutate.company_content as any).update({
      id: task.id,
      values: {
        archived: true
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]" hideCloseButton>
        <DialogHeader>
          <DialogTitle className="flex self-end">
            {/* <span>Edit Task</span> */}
            <Button
            //   variant=""
              size="sm"
              className="gap-2"
              onClick={handleOpenInStudio}
              data-test="open-in-studio-button"
            >
              <Maximize2 className="h-4 w-4" />
              Open in Content Studio
            </Button>
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="task_title">Task Name</Label>
            <Input
              id="task_title"
              value={formData.task_title}
              onChange={(e) => setFormData(prev => ({ ...prev, task_title: e.target.value }))}
              placeholder="Enter task name"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="task_description">Description</Label>
            <Textarea
              id="task_description"
              rows={8}
              value={formData.task_description}
              onChange={(e) => setFormData(prev => ({ ...prev, task_description: e.target.value }))}
              placeholder="Enter task description"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="assigned_to">Assigned To</Label>
            <Select
              value={formData.assigned_to}
              onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_to: value === "unassigned" ? "" : value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select assignee" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassigned">
                  <span className="text-muted-foreground">Unassigned</span>
                </SelectItem>
                {companyMembers.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    <div className="flex items-center gap-2">
                      <span>{member.name}</span>
                      {member.email && (
                        <span className="text-xs text-muted-foreground">({member.email})</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {taskStatuses ? (
                  taskStatuses.map((status: TaskStatus) => (
                    <SelectItem key={status.id} value={status.name}>
                      {status.display_name}
                    </SelectItem>
                  ))
                ) : (
                  // Fallback statuses if company statuses haven't loaded
                  <>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="in progress">In Progress</SelectItem>
                    <SelectItem value="review">Review</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="scheduled_publishing_time">Publish Date & Time</Label>
            <Input
              id="scheduled_publishing_time"
              type="datetime-local"
              value={formData.scheduled_publishing_time}
              onChange={(e) => setFormData(prev => ({ ...prev, scheduled_publishing_time: e.target.value }))}
            />
          </div>

          <div className="flex justify-between items-center mt-4  pt-4 border-t">
            <Button
              type="button"
              variant="destructive"
              onClick={() => {
                archiveTask(task);
                onOpenChange(false);
              }}
              className="flex items-center gap-2"
            >
              <Archive className="h-4 w-4" />
              Archive
            </Button>
        
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export function TaskCreateDialog({ open, onOpenChange, campaignId }: TaskCreateDialogProps) {
  const workspace = useTeamAccountWorkspace();
  const [isLoading, setIsLoading] = useState(false);
  const zero = useZero();
  const [formData, setFormData] = useState({
    task_title: "",
    task_description: "",
    status: "draft",
    assigned_to: "",
    scheduled_publishing_time: "",
  })

  // Get all memberships for this company
  const [memberships] = useZeroQuery(
    zero.query.accounts_memberships.where('account_id', '=', workspace.account.id),
    {
      ttl: '1d'
    }
  );

  // Get all accounts (users) from memberships
  const [accounts] = useZeroQuery(zero.query.accounts, {
    ttl: '1d'
  });

  // Get company members with their account details
  const companyMembers = useMemo(() => {
    if (!memberships || !accounts) return [];

    return memberships.map(membership => {
      const account = accounts.find(acc => acc.id === membership.user_id);
      return {
        id: membership.user_id,
        name: account?.name || 'Unknown User',
        email: account?.email || '',
        role: membership.account_role
      };
    }).filter(member => member.name !== 'Unknown User');
  }, [memberships, accounts]);

  // Fetch company task statuses
  const { data: taskStatuses } = useQuery({
    queryKey: ['taskStatuses', workspace.account.id],
    queryFn: () => getCompanyTaskStatuses(workspace.account.id),
    enabled: !!workspace.account.id,
  })

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        task_title: "",
        task_description: "",
        status: "draft",
        assigned_to: "",
        scheduled_publishing_time: "",
      });
    }
  }, [open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const now = Date.now();
      const taskId = crypto.randomUUID();

      // Prepare the insert values
      const insertValues: any = {
        task_title: formData.task_title,
        task_description: formData.task_description,
        status: formData.status,
        company_id: workspace.account.id,
        campaign_id: campaignId || null,
        created_at: now,
        updated_at: now,
        archived: false,
        is_generating: false,
        content_type: 'task', // Set a default content type for tasks
      };

      // Handle assigned_to properly - explicitly set null for unassignment
      if (formData.assigned_to === "" || formData.assigned_to === "unassigned") {
        insertValues.assigned_to = null;
      } else if (formData.assigned_to) {
        insertValues.assigned_to = formData.assigned_to;
      }

      // Handle scheduled publishing time
      if (formData.scheduled_publishing_time) {
        insertValues.scheduled_publishing_time = new Date(formData.scheduled_publishing_time).getTime();
      }

      await (zero.mutate.company_content as any).insert({
        id: taskId,
        values: insertValues
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to create task:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]" hideCloseButton>
        <DialogHeader>
          <DialogTitle>Create New Task</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="task_title">Task Name</Label>
            <Input
              id="task_title"
              value={formData.task_title}
              onChange={(e) => setFormData(prev => ({ ...prev, task_title: e.target.value }))}
              placeholder="Enter task name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="task_description">Description</Label>
            <Textarea
              id="task_description"
              rows={8}
              value={formData.task_description}
              onChange={(e) => setFormData(prev => ({ ...prev, task_description: e.target.value }))}
              placeholder="Enter task description"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="assigned_to">Assigned To</Label>
            <Select
              value={formData.assigned_to}
              onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_to: value === "unassigned" ? "" : value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select assignee" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassigned">
                  <span className="text-muted-foreground">Unassigned</span>
                </SelectItem>
                {companyMembers.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    <div className="flex items-center gap-2">
                      <span>{member.name}</span>
                      {member.email && (
                        <span className="text-xs text-muted-foreground">({member.email})</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {taskStatuses ? (
                  taskStatuses.map((status: TaskStatus) => (
                    <SelectItem key={status.id} value={status.name}>
                      {status.display_name}
                    </SelectItem>
                  ))
                ) : (
                  // Fallback statuses if company statuses haven't loaded
                  <>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="in progress">In Progress</SelectItem>
                    <SelectItem value="review">Review</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="scheduled_publishing_time">Publish Date & Time</Label>
            <Input
              id="scheduled_publishing_time"
              type="datetime-local"
              value={formData.scheduled_publishing_time}
              onChange={(e) => setFormData(prev => ({ ...prev, scheduled_publishing_time: e.target.value }))}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Task"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
