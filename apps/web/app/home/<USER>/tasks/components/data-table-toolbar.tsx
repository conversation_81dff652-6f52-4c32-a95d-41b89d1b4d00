"use client"

import { Table } from "@tanstack/react-table"
import { X, Filter, Plus } from "lucide-react"
import { Column } from "@tanstack/react-table"
import { Circle, Clock, Check } from "lucide-react"
import { useQuery } from "@tanstack/react-query"
import { getCompanyTaskStatuses } from "~/services/task-status"

// import { DataTableViewOptions } from "./data-table-view-options"
import { ViewSelector, ViewMode } from "./view-selector"

import { Input } from "@kit/ui/input"
import { Button } from "@kit/ui/button"
import { Checkbox } from "@kit/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@kit/ui/dropdown-menu"
import { useZero } from "~/hooks/use-zero"
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { DataTableViewOptions } from "./data-table-view-options"

// Component to handle Content Type filtering
function ContentTypeFilter({ 
  column, 
  data 
}: { 
  column: Column<any, unknown>; 
  data: any[];
}) {
  // Extract unique content types from the data
  const contentTypes = Array.from(new Set(
    data
      .map(item => item.content_type)
      .filter(Boolean)
  )).map(type => ({
    value: type,
    label: type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }));

  const currentFilter = column?.getFilterValue() as string[] | string | undefined;
  const selectedValues = Array.isArray(currentFilter) ? currentFilter : currentFilter ? [currentFilter] : [];

  if (contentTypes.length === 0) {
    return (
      <DropdownMenuItem disabled>
        No content types available
      </DropdownMenuItem>
    );
  }

  const toggleSelection = (value: string) => {
    const newSelection = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value];
    
    column?.setFilterValue(newSelection.length > 0 ? newSelection : undefined);
  };

  const selectAll = () => {
    column?.setFilterValue(contentTypes.map(t => t.value));
  };

  const clearAll = () => {
    column?.setFilterValue(undefined);
  };

  return (
    <>
      {selectedValues.length > 0 && (
        <>
          <DropdownMenuItem 
            onClick={(e) => {
              e.stopPropagation();
              clearAll();
            }} 
            className="text-muted-foreground"
          >
            Clear all
          </DropdownMenuItem>
          <DropdownMenuSeparator />
        </>
      )}
      {selectedValues.length < contentTypes.length && (
        <>
          <DropdownMenuItem 
            onClick={(e) => {
              e.stopPropagation();
              selectAll();
            }} 
            className="text-muted-foreground"
          >
            Select all
          </DropdownMenuItem>
          <DropdownMenuSeparator />
        </>
      )}
      {contentTypes.map((type) => (
        <DropdownMenuItem
          key={type.value}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            toggleSelection(type.value);
          }}
          className="cursor-pointer"
        >
          <div className="flex items-center space-x-2 w-full">
            <Checkbox
              checked={selectedValues.includes(type.value)}
              onCheckedChange={() => toggleSelection(type.value)}
            />
            <span className="flex-1">{type.label}</span>
          </div>
        </DropdownMenuItem>
      ))}
    </>
  );
}

// Component to handle Status filtering with company-specific statuses
function StatusFilter({ 
  column, 
  companyId 
}: { 
  column: Column<any, unknown>; 
  companyId: string;
}) {

  const zero = useZero();

  const [companyStatuses, companyStatusesResult ] = useZeroQuery(
    zero.query.company_task_statuses
    .where("company_id", "=", companyId),
    {
      ttl: '10m'
    }
  );

  const currentFilter = column?.getFilterValue() as string[] | string | undefined;
  const selectedValues = Array.isArray(currentFilter) ? currentFilter : currentFilter ? [currentFilter] : [];

  if (companyStatusesResult.type !== 'complete') {
    return (
      <DropdownMenuItem disabled>
        Loading statuses...
      </DropdownMenuItem>
    );
  }

  if (!companyStatuses || companyStatuses.length === 0) {
    return (
      <DropdownMenuItem disabled>
        No statuses available
      </DropdownMenuItem>
    );
  }

  const toggleSelection = (value: string) => {
    const newSelection = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value];
    
    column?.setFilterValue(newSelection.length > 0 ? newSelection : undefined);
  };

  const selectAll = () => {
    column?.setFilterValue(companyStatuses.map(s => s.name));
  };

  const clearAll = () => {
    column?.setFilterValue(undefined);
  };

  return (
    <>
      {selectedValues.length > 0 && (
        <>
          <DropdownMenuItem 
            onClick={(e) => {
              e.stopPropagation();
              clearAll();
            }} 
            className="text-muted-foreground"
          >
            Clear all
          </DropdownMenuItem>
          <DropdownMenuSeparator />
        </>
      )}
      {selectedValues.length < companyStatuses.length && (
        <>
          <DropdownMenuItem 
            onClick={(e) => {
              e.stopPropagation();
              selectAll();
            }} 
            className="text-muted-foreground"
          >
            Select all
          </DropdownMenuItem>
          <DropdownMenuSeparator />
        </>
      )}
      {companyStatuses.map(status => {
        // Get the icon component
        let IconComponent;
        switch (status.icon) {
          case 'Circle': IconComponent = Circle; break;
          case 'Clock': IconComponent = Clock; break;
          case 'Check': IconComponent = Check; break;
          default: IconComponent = Circle;
        }

        return (
          <DropdownMenuItem
            key={status.name}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              toggleSelection(status.name);
            }}
            className="cursor-pointer"
          >
            <div className="flex items-center space-x-2 w-full">
              <Checkbox
                checked={selectedValues.includes(status.name)}
                onCheckedChange={() => toggleSelection(status.name)}
              />
              <div className="flex items-center space-x-2 flex-1">
                <IconComponent 
                  className="h-4 w-4" 
                  style={{ color: status.color }}
                />
                <span>{status.display_name}</span>
              </div>
            </div>
          </DropdownMenuItem>
        );
      })}
    </>
  );
}

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
  data?: TData[]
  onAddTask?: () => void
}

export function DataTableToolbar<TData>({
  table,
  viewMode,
  onViewModeChange,
  data = [],
  onAddTask,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  
  // Get company ID from the first row for status filtering
  const companyId = (table.getRowModel().rows[0]?.original as any)?.company_id;
  
  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Search tasks..."
          value={(table.getColumn("task_title")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("task_title")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        
        {/* Filters Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-48">
                         {/* Content Type Filter */}
             {table.getColumn("content_type") && (
               <DropdownMenuSub>
                 <DropdownMenuSubTrigger>
                   Content Type
                 </DropdownMenuSubTrigger>
                 <DropdownMenuSubContent>
                   <ContentTypeFilter
                     column={table.getColumn("content_type")!}
                     data={data as any[]}
                   />
                 </DropdownMenuSubContent>
               </DropdownMenuSub>
             )}
             
             {/* Status Filter - Only show if not in kanban view */}
             {table.getColumn("status") && companyId && viewMode !== "kanban" && (
               <DropdownMenuSub>
                 <DropdownMenuSubTrigger>
                   Status
                 </DropdownMenuSubTrigger>
                 <DropdownMenuSubContent>
                   <StatusFilter
                     column={table.getColumn("status")!}
                     companyId={companyId}
                   />
                 </DropdownMenuSubContent>
               </DropdownMenuSub>
             )}
          </DropdownMenuContent>
        </DropdownMenu>

        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        {onAddTask && (
          <Button
            onClick={onAddTask}
            className="h-8 bg-black text-white hover:bg-gray-800"
            size="sm"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Task
          </Button>
        )}
        <ViewSelector viewMode={viewMode} onViewChange={onViewModeChange} />
        {viewMode === "list" && <DataTableViewOptions table={table} />}
      </div>
    </div>
  )
}
