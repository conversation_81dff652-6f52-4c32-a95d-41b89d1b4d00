"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  Table as ReactTable,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import {  useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kit/ui/table"
import { Spinner } from "@kit/ui/spinner"
import { Badge } from "@kit/ui/badge"
import { X } from "lucide-react"
import { Button } from "@kit/ui/button"
import { useQuery } from "@tanstack/react-query"
import { getCompanyTaskStatuses } from "~/services/task-status"

import { DataTablePagination } from "./data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"
import { KanbanView } from "./kanban-view"
import { ViewMode } from "./view-selector"
import { TaskEditDialog } from "./task-edit-dialog"
import { CompanyContent } from "~/types/company-content"
import { SchedulerProvider } from "~/components/calendar/providers/schedular-provider"
import SchedularView from "~/components/calendar/components/schedule/_components/view/schedular-view"
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace"

// Component to show active filters
function FilterIndicator<TData>({ 
  table, 
  data 
}: { 
  table: ReactTable<TData>; 
  data: TData[] 
}) {
  const columnFilters = table.getState().columnFilters;
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();

  const [companyStatuses] = useZeroQuery(zero.query.company_task_statuses
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );
  
  // Filter out the search filter (task_title) to only show dropdown filters
  const activeFilters = columnFilters.filter(filter => filter.id !== "task_title");
  
  if (activeFilters.length === 0) {
    return null;
  }

  const contentTypeFilter = activeFilters.find(f => f.id === "content_type");
  const statusFilter = activeFilters.find(f => f.id === "status");

  // Format content type display name
  const formatContentType = (value: string) => {
    return value.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Get status display name
  const getStatusDisplayName = (statusName: string) => {
    const status = companyStatuses?.find(s => s.name === statusName);
    return status?.display_name;
  };

  // Helper to get array of values from filter
  const getFilterValues = (filter: any): string[] => {
    if (!filter?.value) return [];
    return Array.isArray(filter.value) ? filter.value : [filter.value];
  };

  // Remove a specific value from a filter
  const removeFilterValue = (columnId: string, valueToRemove: string) => {
    const column = table.getColumn(columnId);
    const currentFilter = column?.getFilterValue();
    
    if (Array.isArray(currentFilter)) {
      const newValues = currentFilter.filter(v => v !== valueToRemove);
      column?.setFilterValue(newValues.length > 0 ? newValues : undefined);
    } else {
      column?.setFilterValue(undefined);
    }
  };

  const contentTypeValues = getFilterValues(contentTypeFilter);
  const statusValues = getFilterValues(statusFilter);

  return (
    <div className="space-y-2 mb-4">
      {/* Content Type Badges */}
      {contentTypeValues.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span className="text-xs font-medium text-muted-foreground">Content Types:</span>
          {contentTypeValues.map((value) => (
            <Badge key={value} variant="secondary" className="font-normal">
              {formatContentType(value)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFilterValue("content_type", value)}
                className="ml-2 h-4 w-4 p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.getColumn("content_type")?.setFilterValue(undefined)}
            className="h-6 px-2 text-xs"
          >
            Clear all
            <X className="ml-1 h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Status Badges */}
      {statusValues.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span className="text-xs font-medium text-muted-foreground">Status:</span>
          {statusValues.map((value) => (
            <Badge key={value} variant="secondary" className="font-normal">
              {getStatusDisplayName(value)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFilterValue("status", value)}
                className="ml-2 h-4 w-4 p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.getColumn("status")?.setFilterValue(undefined)}
            className="h-6 px-2 text-xs"
          >
            Clear all
            <X className="ml-1 h-3 w-3" />
          </Button>
        </div>
      )}
      
      {/* Global Clear All Button */}
      {(contentTypeValues.length > 0 || statusValues.length > 0) && (
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              table.getColumn("content_type")?.setFilterValue(undefined);
              table.getColumn("status")?.setFilterValue(undefined);
            }}
            className="h-6 px-2 text-xs"
          >
            Clear all filters
            <X className="ml-1 h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  );
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isLoading: boolean
  loadingMessage?: string
  onAddTask?: () => void
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading,
  loadingMessage,
  onAddTask,
}: DataTableProps<TData, TValue>) {
  const zero = useZero();

  const [user_cache] = useZeroQuery(zero.query.user_cache, {
    ttl: '1d'
  });
  const userCache = user_cache?.[0];

  const [viewMode, setViewMode] = React.useState<ViewMode>("list")
  const [rowSelection, setRowSelection] = React.useState({})
  // Initialize column visibility from user_cache.task_list_columns or use default state
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>(() => {
    if (userCache?.task_list_columns) {
      return userCache.task_list_columns as VisibilityState;
    }
    // Default state - hide certain columns by default
    return {
      updated_at: false,
      // Add other columns you want hidden by default here
      // created_at: false,
    };
  })
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  // Initialize sorting from user_cache.task_sorting_state or use default state
  const [sorting, setSorting] = React.useState<SortingState>(() => {
    if (userCache?.task_sorting_state) {
      return userCache.task_sorting_state as unknown as SortingState;
    }
    // Default state - no sorting applied
    return [];
  })
  const [selectedTask, setSelectedTask] = React.useState<CompanyContent | null>(null)
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);

  // Function to save column visibility to user_cache
  const saveColumnVisibility = React.useCallback((newVisibility: VisibilityState) => {
    if (zero && userCache?.user_id) {
      zero.mutate.user_cache.update({
        user_id: userCache.user_id,
        values: {
          task_list_columns: newVisibility,
        }
      } as any);
    }
  }, [zero, userCache]);

  // Function to save sorting state to user_cache
  const saveSortingState = React.useCallback((newSorting: SortingState) => {
    console.log('newSorting', newSorting, userCache, zero);
    if (zero && userCache?.user_id) {
      zero.mutate.user_cache.update({
        user_id: userCache.user_id,
        values: {
          task_sorting_state: newSorting,
        }
      } as any);
    }
  }, [zero, userCache]);

  // Custom column visibility change handler that also saves to user_cache
  const handleColumnVisibilityChange = React.useCallback((updaterOrValue: VisibilityState | ((old: VisibilityState) => VisibilityState)) => {
    if (typeof updaterOrValue === 'function') {
      setColumnVisibility(prev => {
        const newVisibility = updaterOrValue(prev);
        saveColumnVisibility(newVisibility);
        return newVisibility;
      });
    } else {
      setColumnVisibility(updaterOrValue);
      saveColumnVisibility(updaterOrValue);
    }
  }, [saveColumnVisibility]);

  // Custom sorting change handler that also saves to user_cache
  const handleSortingChange = React.useCallback((updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
    if (typeof updaterOrValue === 'function') {
      setSorting(prev => {
        const newSorting = updaterOrValue(prev);
        saveSortingState(newSorting);
        return newSorting;
      });
    } else {
      setSorting(updaterOrValue);
      saveSortingState(updaterOrValue);
    }
  }, [saveSortingState]);

  // Update sorting state when userCache becomes available
  React.useEffect(() => {
    if (userCache?.task_sorting_state) {
      setSorting(userCache.task_sorting_state as unknown as SortingState);
    }
  }, [userCache?.task_sorting_state]);

  const handleRowClick = React.useCallback((task: CompanyContent) => {
    setSelectedTask(task)
    setIsDialogOpen(true)
  }, [])

  const table = useReactTable({
    data,
    columns: columns as ColumnDef<TData, TValue>[],
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: handleSortingChange,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: handleColumnVisibilityChange,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  // Get filtered data for Kanban view
  const filteredData = React.useMemo(() => {
    if (columnFilters.length === 0) return data

    // Apply the same filters as the table
    return table.getFilteredRowModel().rows.map(row => row.original)
  }, [columnFilters, data, table])

  return (
    <div className="space-y-4">
      <DataTableToolbar
        table={table}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        data={data}
        onAddTask={onAddTask}
      />
      
      {/* Filter Indicator */}
      <FilterIndicator table={table} data={data} />
      
      {viewMode === "list" && 
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id} colSpan={header.colSpan}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      onClick={() => handleRowClick(row.original as CompanyContent)}
                      className="cursor-pointer hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-2">
                          <Spinner className="h-5 w-5" />
                          <span>{loadingMessage ? loadingMessage : "Loading..."}</span>
                        </div>
                      ) : (
                        "No results."
                      )}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <DataTablePagination table={table} />
        </>
      }
      {viewMode === "kanban" && <KanbanView tasks={filteredData as any} />}
      {viewMode === "calendar" && 
      <>
      <SchedulerProvider initialState={filteredData as any}>
        <SchedularView />
      </SchedulerProvider>  
      </>
      }

      {selectedTask && (
        <TaskEditDialog
          task={selectedTask}
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
        />
      )}
    </div>
  )
}
